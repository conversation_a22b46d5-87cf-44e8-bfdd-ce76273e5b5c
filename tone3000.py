import requests
import time
import os
import json
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
from queue import Queue, Empty

# ==============================================================================
# TŘÍDA PRO KOMUNIKACI S API (Připraveno pro finální úpravu)
# ==============================================================================

class Tone3000API:
    def __init__(self, api_key=None, status_callback=None, config_path='~/.tone3000/config.json'):
        self.api_key = api_key
        self.base_url = "https://www.tone3000.com/api/v1"
        self.config_path = os.path.expanduser(config_path)
        self.session = self._load_session()
        self.status_callback = status_callback

    def _log(self, message):
        if self.status_callback:
            self.status_callback(message)

    def _load_session(self):
        if os.path.exists(self.config_path):
            with open(self.config_path, 'r') as f: return json.load(f)
        return None

    def _save_session(self):
        os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
        with open(self.config_path, 'w') as f: json.dump(self.session, f, indent=4)

    def _get_session_tokens(self):
        if not self.api_key:
            raise Exception("API klíč není k dispozici pro vytvoření nového sezení.")
        self._log("Vytvářím nové sezení pomocí API klíče...")
        response = requests.post(f"{self.base_url}/auth/session", json={'api_key': self.api_key})
        response.raise_for_status()
        data = response.json()
        self.session = {
            'access_token': data['access_token'], 'refresh_token': data['refresh_token'],
            'expires_at': time.time() + data['expires_in']
        }
        self._save_session()
        self._log("Nové sezení úspěšně vytvořeno a uloženo.")

    def _refresh_access_token(self):
        self._log("Přístupový token vypršel, obnovuji...")
        if not self.session or 'refresh_token' not in self.session:
            raise Exception("Chybí obnovovací token. Je potřeba nové přihlášení s API klíčem.")
        response = requests.post(f"{self.base_url}/auth/refresh", json={'refresh_token': self.session['refresh_token']})
        response.raise_for_status()
        data = response.json()
        self.session['access_token'] = data['access_token']
        self.session['expires_at'] = time.time() + data['expires_in']
        self._save_session()
        self._log("Přístupový token úspěšně obnoven.")

    def _make_request(self, endpoint, method='GET', parse_json=True, **kwargs):
        if not self.session or self.session.get('expires_at', 0) <= time.time():
            if self.session and 'refresh_token' in self.session:
                self._refresh_access_token()
            else:
                self._get_session_tokens()

        headers = {'Authorization': f"Bearer {self.session['access_token']}", 'Accept': 'application/json'}
        url = f"{self.base_url}/{endpoint}"
        
        try:
            response = requests.request(method, url, headers=headers, **kwargs)
            if response.status_code == 401:
                self._log("Neautorizovaný požadavek (401), zkouším obnovit token...")
                self._refresh_access_token()
                headers['Authorization'] = f"Bearer {self.session['access_token']}"
                response = requests.request(method, url, headers=headers, **kwargs)
            
            response.raise_for_status()
            
            if parse_json: return response.json()
            return response
        except requests.exceptions.HTTPError as e:
            # Zalogujeme text odpovědi pro lepší diagnostiku
            self._log(f"HTTP Chyba: {e.response.status_code} - Odpověď serveru: {e.response.text[:500]}...")
            raise e
        except Exception as e:
            self._log(f"Obecná chyba požadavku: {e}")
            raise e

    def get_user_info(self):
        return self._make_request('user')

    def get_tones(self, sort='popular', page=1, query=None):
        # ============================ K OPRAVĚ ============================
        # Chyba 404 Not Found potvrdila, že název endpointu 'presets' je nesprávný.
        # Nahraďte 'presets' v řádku níže skutečným názvem endpointu,
        # který zjistíte z analýzy síťové komunikace v prohlížeči (karta Network).
        # Může to být např. 'search', 'browse' nebo něco úplně jiného.
        endpoint_name = 'presets' 
        # =================================================================
        
        params = {'sort': sort, 'page': page}
        if query:
            params['query'] = query
        
        self._log(f"Získávám seznam tónů (Endpoint: /{endpoint_name}, Třídění: {sort}, Strana: {page})")
        return self._make_request(endpoint_name, params=params)

    def download_tone(self, tone_id, download_path='.'):
        # Tento endpoint je pravděpodobně správný, ale pokud by selhal, postup je stejný
        endpoint = f'presets/{tone_id}/download'
        self._log(f"Žádám o stažení tónu z endpointu: {endpoint}")
        
        response = self._make_request(endpoint, parse_json=False, stream=True)

        content_disposition = response.headers.get('content-disposition')
        filename = f"{tone_id}.zip" # Výchozí název
        if content_disposition:
            parts = content_disposition.split('filename=')
            if len(parts) > 1:
                filename = parts[1].strip("\"'")

        file_path = os.path.join(download_path, filename)
        
        self._log(f"Stahuji soubor '{filename}' do '{download_path}'...")
        with open(file_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        self._log(f"Tón byl úspěšně stažen: {file_path}")
        return file_path

# ==============================================================================
# TŘÍDA PRO GRAFICKÉ ROZHRANÍ (beze změn)
# Vložte sem kompletní třídu Tone3000App z předchozí odpovědi.
# Její kód je v pořádku a není třeba jej měnit.
# ==============================================================================
class Tone3000App:
    def __init__(self, root):
        self.root = root
        self.root.title("Tone3000 Manager")
        self.root.geometry("850x650")
        
        self.client = None
        self.request_queue = Queue()
        self.current_page = 1
        self.root.after(100, self.process_queue)

        style = ttk.Style()
        style.theme_use('clam')
        style.configure("Treeview.Heading", font=(None, 10, 'bold'))

        main_paned = ttk.PanedWindow(root, orient=tk.VERTICAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        top_frame = ttk.Frame(main_paned)
        main_paned.add(top_frame, weight=1)
        bottom_frame = ttk.Frame(main_paned)
        main_paned.add(bottom_frame, weight=0)

        self._create_controls_ui(top_frame)
        self._create_log_ui(bottom_frame)
        self._try_auto_login()

    def _create_controls_ui(self, parent):
        parent.columnconfigure(0, weight=1)
        parent.rowconfigure(2, weight=1)

        auth_frame = ttk.LabelFrame(parent, text="Přihlášení", padding=10)
        auth_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        auth_frame.columnconfigure(1, weight=1)

        ttk.Label(auth_frame, text="API klíč:").grid(row=0, column=0, sticky="w", padx=5)
        self.api_key_entry = ttk.Entry(auth_frame, width=50)
        self.api_key_entry.grid(row=0, column=1, sticky="ew", padx=5)
        self.connect_button = ttk.Button(auth_frame, text="Přihlásit", command=self.connect)
        self.connect_button.grid(row=0, column=2, padx=5)
        self.status_label = ttk.Label(auth_frame, text="Nejste přihlášeni.", foreground="red")
        self.status_label.grid(row=1, column=0, columnspan=3, sticky="w", pady=5, padx=5)

        self.tones_frame = ttk.LabelFrame(parent, text="Procházet tóny", padding=10)
        self.tones_frame.grid(row=1, column=0, sticky="ew", pady=(0, 10), rowspan=2)
        self.tones_frame.columnconfigure(0, weight=1)
        self.tones_frame.rowconfigure(1, weight=1)

        controls_frame = ttk.Frame(self.tones_frame)
        controls_frame.grid(row=0, column=0, columnspan=2, sticky="ew", pady=5)
        controls_frame.columnconfigure(3, weight=1)

        ttk.Label(controls_frame, text="Třídit podle:").pack(side=tk.LEFT, padx=(0, 5))
        self.sort_var = tk.StringVar(value="popular")
        self.sort_menu = ttk.OptionMenu(controls_frame, self.sort_var, "popular", "popular", "newest", "top")
        self.sort_menu.pack(side=tk.LEFT)
        self.fetch_button = ttk.Button(controls_frame, text="Načíst", command=self.fetch_tones)
        self.fetch_button.pack(side=tk.LEFT, padx=10)

        self.download_button = ttk.Button(controls_frame, text="Stáhnout vybraný", command=self.download_selected)
        self.download_button.pack(side=tk.RIGHT, padx=5)

        self.tones_tree = ttk.Treeview(self.tones_frame, columns=("id", "name", "author", "stomp_a", "stomp_b", "amp", "cab"), show="headings")
        self.tones_tree.grid(row=1, column=0, sticky="nsew")
        vsb = ttk.Scrollbar(self.tones_frame, orient="vertical", command=self.tones_tree.yview)
        vsb.grid(row=1, column=1, sticky="ns")
        self.tones_tree.configure(yscrollcommand=vsb.set)
        
        self.tones_tree.heading("id", text="ID")
        self.tones_tree.heading("name", text="Název presetu")
        self.tones_tree.heading("author", text="Autor")
        self.tones_tree.heading("stomp_a", text="Stomp A")
        self.tones_tree.heading("stomp_b", text="Stomp B")
        self.tones_tree.heading("amp", text="Zesilovač")
        self.tones_tree.heading("cab", text="Kabinet")
        
        for col in self.tones_tree['columns']: self.tones_tree.column(col, anchor='w')
        self.tones_tree.column("id", width=60, anchor='center')
        self.tones_tree.column("name", width=220)
        self.tones_tree.column("author", width=100)

        pagination_frame = ttk.Frame(self.tones_frame)
        pagination_frame.grid(row=2, column=0, columnspan=2, sticky="ew", pady=5)
        pagination_frame.columnconfigure(1, weight=1)
        self.prev_button = ttk.Button(pagination_frame, text="<< Předchozí", command=self.prev_page)
        self.prev_button.pack(side=tk.LEFT)
        self.page_label = ttk.Label(pagination_frame, text="Strana 1")
        self.page_label.pack(side=tk.LEFT, padx=10)
        self.next_button = ttk.Button(pagination_frame, text="Další >>", command=self.next_page)
        self.next_button.pack(side=tk.LEFT)

        self.set_ui_state(False)

    def _create_log_ui(self, parent):
        log_frame = ttk.LabelFrame(parent, text="Log", padding=5)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(5,0))
        self.log_text = tk.Text(log_frame, height=8, state='disabled', wrap=tk.WORD, font=("Consolas", 9))
        sb = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.config(yscrollcommand=sb.set)
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        sb.pack(side=tk.RIGHT, fill=tk.Y)

    def log(self, message):
        self.log_text.config(state='normal')
        self.log_text.insert(tk.END, f"[{time.strftime('%H:%M:%S')}] {message}\n")
        self.log_text.config(state='disabled')
        self.log_text.see(tk.END)

    def set_ui_state(self, logged_in):
        state = 'normal' if logged_in else 'disabled'
        for w in [self.fetch_button, self.download_button, self.sort_menu, self.prev_button, self.next_button]:
            w.config(state=state)

    def threaded_task(self, task, *args):
        self.set_ui_state(False) # Zakázat UI během operace
        thread = threading.Thread(target=task, args=args, daemon=True)
        thread.start()

    def connect(self):
        api_key = self.api_key_entry.get()
        if not api_key:
            messagebox.showerror("Chyba", "Prosím, zadejte svůj API klíč.")
            return
        self.status_label.config(text="Přihlašuji...", foreground="orange")
        self.connect_button.config(state="disabled")
        self.threaded_task(self._perform_connection, api_key)

    def _try_auto_login(self):
        if os.path.exists(os.path.expanduser('~/.tone3000/config.json')):
            self.log("Nalezeno uložené sezení, zkouším automatické přihlášení...")
            self.status_label.config(text="Přihlašuji z uloženého sezení...", foreground="orange")
            self.threaded_task(self._perform_connection)

    def _perform_connection(self, api_key=None):
        try:
            self.client = Tone3000API(api_key, lambda msg: self.request_queue.put(('log', msg)))
            user_info = self.client.get_user_info()
            self.request_queue.put(('connection_success', user_info))
        except Exception as e:
            self.request_queue.put(('connection_error', e))

    def fetch_tones(self, page=1):
        self.current_page = page
        self.page_label.config(text=f"Strana {self.current_page}")
        self.threaded_task(self._perform_fetch, self.sort_var.get(), self.current_page)

    def prev_page(self):
        if self.current_page > 1: self.fetch_tones(self.current_page - 1)
    
    def next_page(self):
        self.fetch_tones(self.current_page + 1)

    def _perform_fetch(self, sort_by, page):
        try:
            tones_data = self.client.get_tones(sort=sort_by, page=page)
            self.request_queue.put(('tones_success', tones_data))
        except Exception as e:
            self.request_queue.put(('tones_error', e))

    def download_selected(self):
        selected = self.tones_tree.selection()
        if not selected:
            messagebox.showinfo("Informace", "Nejprve vyberte tón ze seznamu.")
            return
        tone_id = self.tones_tree.item(selected[0], "values")[0]
        download_dir = filedialog.askdirectory(title="Vyberte složku pro stažení")
        if not download_dir: return
        self.threaded_task(self._perform_download, tone_id, download_dir)

    def _perform_download(self, tone_id, path):
        try:
            file_path = self.client.download_tone(tone_id, path)
            self.request_queue.put(('download_success', file_path))
        except Exception as e:
            self.request_queue.put(('download_error', e))

    def process_queue(self):
        try:
            while True:
                msg_type, data = self.request_queue.get_nowait()
                if msg_type == 'log': self.log(data)
                elif msg_type == 'connection_success': self.handle_connection_success(data)
                elif msg_type == 'connection_error': self.handle_connection_error(data)
                elif msg_type == 'tones_success': self.handle_tones_success(data)
                elif msg_type == 'tones_error': self.handle_tones_error(data)
                elif msg_type == 'download_success': self.handle_download_success(data)
                elif msg_type == 'download_error': self.handle_download_error(data)
        except Empty:
            pass
        finally:
            self.root.after(100, self.process_queue)

    def handle_connection_success(self, user_info):
        username = user_info.get('username', 'N/A')
        self.status_label.config(text=f"Přihlášen jako: {username}", foreground="green")
        self.log(f"Úspěšně ověřeno pro uživatele: {username}.")
        self.connect_button.config(state="normal")
        self.fetch_tones()

    def handle_connection_error(self, error):
        self.status_label.config(text="Chyba přihlášení!", foreground="red")
        self.log(f"Chyba při přihlašování: {error}")
        messagebox.showerror("Chyba přihlášení", f"Nepodařilo se připojit k Tone3000 API.\n\nZkontrolujte API klíč a připojení.\nDetail: {error}")
        self.connect_button.config(state="normal")
        self.set_ui_state(False)

    def handle_tones_success(self, response_data):
        self.set_ui_state(True)
        self.tones_tree.delete(*self.tones_tree.get_children())
        
        tones_list = response_data.get('data', [])
        if not tones_list:
            self.log("Na této stránce nebyly nalezeny žádné další tóny.")
            self.next_button.config(state='disabled') # Žádné další tóny
        
        for tone in tones_list:
            preset_data = tone.get('preset', {})
            self.tones_tree.insert("", tk.END, values=(
                tone.get('id', 'N/A'),
                tone.get('name', 'N/A'),
                tone.get('user', {}).get('username', 'N/A'),
                preset_data.get('stomp_a_model', '-'),
                preset_data.get('stomp_b_model', '-'),
                preset_data.get('amp_model', '-'),
                preset_data.get('cab_model', '-')
            ))
        self.log(f"Zobrazeno {len(tones_list)} tónů.")

    def handle_tones_error(self, error):
        self.log(f"Chyba při načítání tónů: {error}")
        messagebox.showerror("Chyba API", f"Nepodařilo se načíst tóny.\n\nDetail: {error}")
        self.set_ui_state(True) 

    def handle_download_success(self, file_path):
        messagebox.showinfo("Úspěch", f"Tón byl úspěšně stažen do:\n{file_path}")
        self.set_ui_state(True)

    def handle_download_error(self, error):
        messagebox.showerror("Chyba stahování", f"Nepodařilo se stáhnout tón.\n\nDetail: {error}")
        self.set_ui_state(True)


# ==============================================================================
# SPUŠTĚNÍ APLIKACE
# ==============================================================================

if __name__ == "__main__":
    root = tk.Tk()
    app = Tone3000App(root)
    root.mainloop()